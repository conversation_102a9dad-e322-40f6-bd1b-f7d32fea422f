import requests
import json
import time
import base64 
import os

# Replace with your API key
API_KEY = os.getenv("GOOGLE_TTS_API_KEY")
# Text to synthesize
#text = "Hello, this is a test of Google Text-to-Speech."

def generate_voice(text, output_path):
    # API endpoint
    url = f"https://texttospeech.googleapis.com/v1/text:synthesize?key={API_KEY}"

    # Request headers and body
    headers = {"Content-Type": "application/json; charset=utf-8"}
    data = {
        "input": {"text": text},
        "voice": {
            "languageCode": "en-US",  # Language code (en-US, nl-NL, etc.)
            "name": "en-US-Wavenet-D", #"en-US-Wavenet-D",  # High-quality voice
            "ssmlGender": "NEUTRAL"
            #"languageCode": "nl-NL",  # Dutch (Netherlands)
            #"name": "nl-NL-Wavenet-B",  # Dutch female voice
            #"ssmlGender": "FEMALE"  # Gender alignment for the voice
        },
        "audioConfig": {
            "audioEncoding": "MP3"  # Output format (MP3, LINEAR16, etc.)
        }
    }

    # Send the request
    response = requests.post(url, headers=headers, data=json.dumps(data))
    if response.status_code == 200:
        # Save the audio to a file
        audio_base64 = response.json()["audioContent"]
        # Decode the audio content
        audio_binary = base64.b64decode(audio_base64)
        with open(output_path, "wb") as f:
            f.write(audio_binary)
        print("Audio saved")
    else:
        print(f"Error: {response.status_code}, {response.text}")


def generate_all_voice(data, folder):
    scenes = data['prompts']

    for idx, scene in enumerate(scenes, start=1):
        text = scene['text']
        generate_voice(text, f"{folder}/{idx}.mp3")


