import requests
import json
from time import sleep
import ollama

def generate_video_prompts(company_details):
    """
    Generates video prompts using DeepSeek-8B via Ollama
    Returns JSON structure with prompts and overlay texts
    """
    # Construct the system prompt
    system_prompt = """Act as a marketing and video scripting assistant. Generate 5 video concepts to promote a company. For each concept, create:
    1. company promotion text (at least eight words long and at most twelve words long) (these sentences should promote the copmpany based on their description in an engaging way)
    2. A detailed video generation prompt (no more than fifteen words) that aligns with the text
    You can use the following camera movement instruction inside the video generation prompt to make it more active:
    It is good when the video generation prompt tries to describe the company workplace and shows (in one or two prompts) team collaboration.
    So the video generation prompt should describe the environment of the scene, where it takes place, and what objects are there, and which interactions happen there, and the camera movement(s) of the scene.
    Use words that describe the scene and objects inside the scene, don't use pronouns like (I, We, You).
    Note: To ensure optimal results, it is recommended to use no more than 3 combined camera movement instructions.
    1.1 Supported 15 Camera Movement Instructions (Enumerated Values)
    -Truck: [Truck left], [Truck right]
    -Pan: [Pan left], [<PERSON> right]
    -Push: [Push in], [Pull out]
    -Pedestal: [Pedestal up], [Pedestal down]
    -Tilt: [Tilt up], [Tilt down]
    -Zoom: [Zoom in], [Zoom out]
    -Shake: [Shake]
    -Follow: [Tracking shot]
    -Static: [Static shot]
    1.2 Supports Single and Combined Camera Movements
    -Single Camera Movement: For example, [Tilt Left] indicates a single camera movement.
    -Multiple Simultaneous Movements: Movements within the same group take effect simultaneously. For example, [Tilt Left, Pan Right] indicates two combined movements that take effect at the same time.
    -Sequential Movements: Movements inserted earlier in the prompt take effect first. For example, in the prompt description ”xxx[Tilt Left], xxx[Pan Right]“ , the video will first execute the ’Tilt Left’movement, followed by the ’Pan Right‘ movement.

    Some good video generation prompt examples are: 
    "A top view of a futurisitc city with a lot of tall buildings [Zoom Out]",
    "factory full of machines working in full power[Push in,Pedestal up][Shake,Tracking shot]",
    "A rocket launching viewed from ground perspective[Truck left,Pedestal up]",
    "panorama of a waterfall cascading down a cliff into a jungle",
    "Tall ancient redwood trees reaching into misty clouds [Push in,Pedestal up]",
    "A tech team in a modern office designing autonomous drones, with blue brand accents. [Zoom In]"
    Return 5 objects containing "text" and "prompt" fields strictly in the following format:
    {
      "prompts" : [
    {
    "text": "...",
    "prompt" : "..."
    }
    ,
    {
    "text": "...",
    "prompt" : "..."
    }
    ,
    {
    "text": "...",
    "prompt" : "..."
    }
    ,
    {
    "text": "...",
    "prompt" : "..."
    }
    ,
    {
    "text": "...",
    "prompt" : "..."
    }
    ,
    ]
    }
    return json only.
    """

    # Construct the user prompt
    user_prompt = f"""
    Using the following company details, generate 5 video concepts following the specified JSON format.
    **Company Details**:
    - Name: {company_details['name']}
    - Description: {company_details['description']}
    """

    try:
        # Generate response using Ollama library
        response = ollama.chat(
            model='deepseek-r1:8b',
            messages=[
                {'role': 'system', 'content': system_prompt},
                {'role': 'user', 'content': user_prompt}
            ],
            options={'format': 'json'},
            stream=False
        )
        #print(response)
        #print("hano")
        #print('message' in response)
        
        # Process the response content
        if 'message' in response:
            raw_content = response['message']['content'].strip()
            
            think_start = raw_content.find("<think>") + len("<think>")
            think_end = raw_content.find("</think>")
            think_content = raw_content[think_start:think_end].strip()
            #print(think_content)
            json_part = raw_content[think_end + len("</think>"):].strip()
            json_lines = json_part.split("\n")
            if json_lines[0] == "```json" and json_lines[-1] == "```":
                json_content = "\n".join(json_lines[1:-1]).strip()
            else:
                json_content = json_part  # Fallback if markers are missing
            #print(json_content)
            #hanoishano

            # Clean JSON response if wrapped in markdown
            #if raw_content.startswith('```json'):
            #    raw_content = raw_content[7:-3].strip()
            #print('hanhon')
            #print(raw_content)
            # Parse the JSON
            #return json.loads(json_content)
            #json_content = json_content.replace('`', '"')
            #json_content = '{' + json_content + '}'
            json_content = json_content.replace('\n', '')
            return json.loads(json_content)
            #return json_content
        
        return None

    except Exception as e:
        print(f"Error generating prompts: {e}")
        return None

    except requests.exceptions.RequestException as e:
        print("API request failed:", e)
        return None

# Example company details
#company_info = {
#    "name": "Loopinz",
#    "description": "Loop onto a mission to redefine recruitment by focusing on what truly matters. Loop into a personalized experience because we believe recruitment is about more than just a CV. Loop towards connections that help companies find their perfect match and candidates discover opportunities that align with their ambitions. 'Loopinz – Creating connections where work truly feels like home'"
#}
def generate_prompts(data):
    company_info = data

    # Generate the video prompts
    video_concepts = generate_video_prompts(company_info)

    if video_concepts:
        # Save to JSON file
        print("Successfully generated video prompts!")
        return video_concepts
    else:
        print("Failed to generate prompts")