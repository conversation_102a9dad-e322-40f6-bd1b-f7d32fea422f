import os
from moviepy.editor import VideoFileClip, AudioFileClip, TextClip, CompositeVideoClip
from moviepy.audio.AudioClip import AudioArrayClip
from moviepy.audio.AudioClip import concatenate_audioclips
import moviepy.audio.fx.all as afx 
from moviepy.video.fx.all import speedx  # Correct import for speedx
from VideoManager.fix_ratio import process_video

from moviepy.config import change_settings
import numpy as np
import json

# Windows magick installation likes not to see magick executable in PATR
if (os.getenv("USING_WINDOWS") == "1"):
    print("WINDOWS MAGICK PATH")
    change_settings({"IMAGEMAGICK_BINARY": r"C:\\Program Files\\ImageMagick-7.1.1-Q16-HDRI\\magick.exe"})
else:
    print("LINUX MAGICK PATH")
    change_settings({"IMAGEMAGICK_BINARY": "/usr/bin/convert"})

def combine_audios_with_video(data, folder):
    # Load the video and its original audio
    
    scenes = data['prompts']

    #for audio_name, video_name, config in zip(audios, videos, text_configs):
    for idx, scene in enumerate(scenes, start=1):

        process_video(f"{folder}/{idx}.mp4", f"{folder}/{idx}_cropped.mp4")

        #### Replace the original video with the cropped video
        os.remove(f"{folder}/{idx}.mp4")
        os.rename(f"{folder}/{idx}_cropped.mp4", f"{folder}/{idx}.mp4")
        ####
        video = VideoFileClip(f"{folder}/{idx}.mp4")
        if os.path.exists(f"{folder}/{idx}.mp3"):
            audio = AudioFileClip(f"{folder}/{idx}.mp3")
        else:
            audio = AudioFileClip(f"{folder}/{idx}.wav")
        
        video_duration = video.duration
        audio_duration = audio.duration
        video_width, video_height = video.size
        if audio_duration < video_duration:
            # Create silent audio array
            silent_duration = video_duration - audio_duration
            silent_array = np.zeros((int(silent_duration * audio.fps), 1))  # Mono channel
            silent_clip = AudioArrayClip(silent_array, fps=audio.fps)
            
            # Concatenate original audio with silence
            audio = concatenate_audioclips([audio, silent_clip])
            
        elif audio_duration > video_duration:
            # Speed up audio to fit
            speed_factor = audio_duration / video_duration
            audio = audio.fx(speedx, speed_factor)

        video = video.set_audio(audio)
        print("SET AUDIO")
        txt_font = "roboto"
        txt_color = "white"
        txt_fontsize = 40

        txt_clip = (
            TextClip(
                txt=scene["text"],
                fontsize=txt_fontsize,
                font=txt_font,
                size=(video_width*3/4, None),
                color=txt_color,
                method='caption',
                align="center",
                #stroke_color="black",
                #stroke_width=1,
                #method="label"  # Required for stroke effects
            )
            .set_position(("center", video_height*3/4))
            .set_start(0.1)
            .set_duration(video.duration-0.1)
        )
        print ("Created textd clip1")
        txt_clip_stroke = (TextClip( scene["text"],
                                 fontsize=txt_fontsize,
                                 color=txt_color,
                                 font=txt_font,
                                 stroke_width=1,
                                 stroke_color="black",
                                 size=(video_width*3/4 + 10, None),
                                 method='caption',
                                 align="center",
                                )
                                .set_position(("center", video_height*3/4))
                                .set_start(0.1)
                                .set_duration(video.duration-0.1)
        )
        print ("Created textd clip2")

        output_dir = f"./{folder}/combined"
        os.makedirs(output_dir, exist_ok=True)  # Creates folder if it doesn't exist

        video = CompositeVideoClip([video, txt_clip_stroke, txt_clip])
        video.write_videofile(
            f"./{folder}/combined/{idx}_comb.mp4",
            codec="libx264",
            audio_codec="aac"
        )
        
        video.close()
        audio.close()
        txt_clip.close()
        print ("END")

#combine_audios_with_video("vid_prompts.json")