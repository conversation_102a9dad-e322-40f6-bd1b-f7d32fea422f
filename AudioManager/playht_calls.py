from pyht import Client
from dotenv import load_dotenv
from pyht.client import TTSOptions, Language
import os
import json

playht_api_key = os.getenv("PLAYHT_API_KEY")
def generate_voice(data, folder):
    
    scenes = data['prompts']
    
    client = Client(
        user_id="HgotCzvl0LRaVpV0aLoeX6uYkH92",
        api_key=playht_api_key,
    )
    options = TTSOptions(voice="s3://voice-cloning-zero-shot/d712cad5-85db-44c6-8ee0-8f4361ed537b/eleanorsaad2/manifest.json", language=Language.DUTCH)
    
    for idx, scene in enumerate(scenes, start=1):
        text = scene['text']
        # Open a file to save the audio
        with open(f"{folder}/{idx}.wav", "wb") as audio_file:
            for chunk in client.tts(text, options, voice_engine = 'Play3.0-mini', protocol="http"):
                # Write the audio chunk to the file
                audio_file.write(chunk)
