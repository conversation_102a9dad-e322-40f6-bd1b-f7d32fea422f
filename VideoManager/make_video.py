import os
import subprocess

# Set your video folder path here
#VIDEO_FOLDER = "avideos_processed"
#OUTPUT_FILE = "Final_Video_Loops(vid+aud+txt).mp4"

def combine_videos(VIDEO_FOLDER, OUTPUT_FILE):
    # Get all mp4 files in the folder
    video_files = [f for f in os.listdir(VIDEO_FOLDER) if f.endswith(".mp4")]
    video_files.sort()  # Sort alphabetically
    print(video_files)
    if not video_files:
        print("No MP4 files found in the directory.")
        return
    
    # Create a temporary file listing all videos to concatenate
    list_file = os.path.join(VIDEO_FOLDER, "filelist.txt")
    with open(list_file, "w") as f:
        for video in video_files:
            f.write(f"file '{video}'\n")
    
    try:
        # Use FFmpeg to concatenate videos
        subprocess.run([
            "ffmpeg",
            "-f", "concat",
            "-safe", "0",
            "-i", list_file,
            "-c", "copy",
            OUTPUT_FILE
        ], check=True)
        print(f"Successfully combined videos into {OUTPUT_FILE}")
    except subprocess.CalledProcessError as e:
        print(f"Error combining videos: {e}")
    finally:
        # Clean up temporary file
        os.remove(list_file)

if __name__ == "__main__":
    combine_videos()