services:
  web:
    build: .
    ports:
      - "5000:5000"
    depends_on:
      - ollama
    env_file:
      - .env
    volumes:
      - videos_data:/app/Videos
    restart: always

  ollama:
    image: ollama/ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: always
    entrypoint: >
      sh -c "ollama serve & sleep 5 && ollama pull deepseek-r1:8b && tail -f /dev/null"

volumes:
  ollama_data:
  videos_data:
