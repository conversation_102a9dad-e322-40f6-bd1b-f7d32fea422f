import requests
import os

def download_image(url, save_path):
    print("SAVE",save_path)
    os.makedirs(save_path, exist_ok=True)

    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/122.0.0.0 Safari/537.36"
        )
    }

    response = requests.get(url, stream=True, headers=headers)
    if response.status_code == 200:
        with open(save_path + "/image.png", 'wb') as file:
            for chunk in response.iter_content(1024):
                file.write(chunk)
        print(f"Image saved as {save_path}")
    else:
        print(response.text)
        print("Failed to download image")