import argparse
from PIL import Image

def resize_image(input_path, output_path, target_size=(720, 720)):
    # Open the image and convert if necessary
    print (f"INPUT PATH {input_path}")
    image = Image.open(input_path + "/image.png")
    original_mode = image.mode
    if original_mode not in ['RGB', 'RGBA']:
        image = image.convert('RGB')
        original_mode = 'RGB'

    # Calculate new dimensions
    original_width, original_height = image.size
    target_width, target_height = target_size
    ratio = min(target_width / original_width, target_height / original_height)
    new_size = (int(original_width * ratio), int(original_height * ratio))

    # Resize the image
    resized_image = image.resize(new_size, Image.Resampling.LANCZOS)

    # Create new image with appropriate background
    if original_mode == 'RGBA':
        new_image = Image.new('RGBA', target_size, (0, 0, 0, 0))
    else:
        new_image = Image.new('RGB', target_size, (255, 255, 255))

    # Calculate position to center the resized image
    position = (
        (target_width - new_size[0]) // 2,
        (target_height - new_size[1]) // 2
    )

    # Paste the resized image
    if original_mode == 'RGBA':
        new_image.paste(resized_image, position, resized_image)
    else:
        new_image.paste(resized_image, position)

    new_image.save(output_path + "/image.png")
