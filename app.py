import os
import uuid
from dotenv import load_dotenv
from flask import Flask, request, jsonify, send_file
from pydantic import BaseModel, ValidationError
from typing import Optional

from PromptManager.gen_prompts import generate_prompts

from AudioManager.playht_calls import generate_voice
from AudioManager.google_tts import generate_all_voice

from ImageManager.download_img import download_image
from ImageManager.add_company_imgs import add_company_imgs
from ImageManager.resize_company_images_to720 import resize_image
from ImageManager.create_subfolders import create_subfolders

from VideoManager.send_requests_hailuo import generate_videos
from VideoManager.comb_aud_vid import combine_audios_with_video
from VideoManager.combine_videos import combine_videos
from VideoManager.add_music import add_music

class RequestModel(BaseModel):
    name: str
    description: str
    industry: str

app = Flask(__name__)

@app.route('/generate-prompts', methods=['POST'])
def get_prompts():
    try:
        try:
            data = request.get_json()
            data = dict(RequestModel(**data))  # Validate JSON
        except ValidationError as e:
            return jsonify({"error": e.errors()}), 400


        if not data:
            return jsonify({"error": "Invalid input, JSON expected"}), 400
        
        data = generate_prompts(data)
        return jsonify(data)
    except ValidationError as e:
        return jsonify({"error": e.errors()}), 400

@app.route('/generate-video', methods=['POST'])
def run_pipeline():
    try:
        random_uuid = uuid.uuid4()

        if not os.path.exists(f"./{random_uuid}"):
            os.mkdir(f"./{random_uuid}")

        try:
            data = request.get_json()
            #data = dict(RequestModel(**data))  # Validate JSON
        except ValidationError as e:
            return jsonify({"error": e.errors()}), 400

        if not data["prompts"]:
            return jsonify({"error": "Invalid input, prompts expected"}), 400

        if "team_img" in data.keys():
            create_subfolders(random_uuid)
            download_image(data["team_img"], f"./{random_uuid}/team_img")
            resize_image(f"./{random_uuid}/team_img", f"./{random_uuid}/team_img_resized")

        if "office_img" in data.keys():
            create_subfolders(random_uuid)
            download_image(data["office_img"], f"{random_uuid}/office_img")
            resize_image(f"./{random_uuid}/office_img", f"./{random_uuid}/office_img_resized")
        
        lang = "en-US"
        if data["lang"]:
            lang = "en-US" if "en" in data["lang"] else "nl-NL"

        data = add_company_imgs(data, random_uuid)
        generate_videos(data, random_uuid)
        if lang == "en-US":
            generate_all_voice(data, random_uuid)
        else:
            generate_voice(data, random_uuid) #function from playht_calls.py - for NL language
        combine_audios_with_video(data, random_uuid)
        if not os.path.exists(f"./{random_uuid}/combined"):
            os.mkdir(f"./{random_uuid}/combined")
        combine_videos(f"./{random_uuid}/combined", f"./{random_uuid}/{random_uuid}.mp4")
        #process_video(f"./{random_uuid}/{random_uuid}.mp4", f"./{random_uuid}/{random_uuid}_cropped.mp4") #function from fix_ratio.py
        add_music(f"./{random_uuid}/{random_uuid}.mp4", "BackgroundMusic/msc.mp3", f"./Videos/{random_uuid}.mp4")
        
        return jsonify({"uid": random_uuid})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/fetch-video/<video_id>', methods=['GET'])
def get_video(video_id):
    # Construct the path to the video file
    video_path = f"./Videos/{video_id}.mp4"

    # Check if the file exists
    if not os.path.exists(video_path):
        abort(404, description="Video not found")

    # Check if ?download=true was passed
    download = request.args.get('download', 'false').lower() == 'true'

    return send_file(
        video_path,
        mimetype='video/mp4',
        as_attachment=download,
        download_name=f"{video_id}.mp4"
    )

if __name__ == '__main__':
    if (os.getenv("USING_WINDOWS") == "1"):
        print("WINDOWS")
    else:
        print("LINUX")
    app.run(debug=True)

