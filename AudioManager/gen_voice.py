##### note this is inside the coqui virtual environment
# 3️⃣ Initalize a pipeline
from kokoro import KPipeline
from IPython.display import display, Audio
import soundfile as sf
import json
# 🇺🇸 'a' => American English
# 🇬🇧 'b' => British English
# 🇫🇷 'f' => French fr-fr
# 🇮🇳 'h' => Hindi hi
pipeline = KPipeline(lang_code='a') # make sure lang_code matches voice

def generate_voice(video_prompts_json):
    with open(video_prompts_json) as f:
        scenes = json.load(f)['prompts']

    for idx, scene in enumerate(scenes, start=1):
        text = scene['text']
        # 4️⃣ Generate, display, and save audio files in a loop.
        generator = pipeline(
            text, voice='af_bella', # <= change voice here
            speed=1, split_pattern=r'\n+'
        )
        for i, (gs, ps, audio) in enumerate(generator):
            print(i)  # i => index
            print(gs) # gs => graphemes/text
            print(ps) # ps => phonemes
            #display(Audio(data=audio, rate=24000, autoplay=i==0))
            sf.write(f'{idx}.wav', audio, 24000) # save each audio file
