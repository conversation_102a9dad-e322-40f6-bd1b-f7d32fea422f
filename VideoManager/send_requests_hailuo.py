import os
import time
import requests
import json
import base64
import cv2


from VideoManager.imgtovid_effects import generate_zoom_in_video, generate_zoom_out_video

api_key = os.getenv("MINIMAXI_API_KEY")
#prompt = "Description of your video"

#output_file_name = "0.mp4"

def invoke_video_generation(prompt)->str:
    print("-----------------Submit video generation task-----------------")
    url = "https://api.minimaxi.chat/v1/video_generation"
    model = "T2V-01-Director"
    payload = json.dumps({
      "prompt": prompt,
      "model": model
    })
    headers = {
      'authorization': 'Bearer ' + api_key,
      'content-type': 'application/json',
    }
    response = requests.request("POST", url, headers=headers, data=payload)
    print(response.text)
    task_id = response.json()['task_id']
    print("Video generation task submitted successfully, task ID.："+task_id)
    return task_id

def invoke_imgtovid_generation(prompt, img_path)->str:
    print("-----------------Submit Image to video generation task-----------------")
    url = "https://api.minimaxi.chat/v1/video_generation"
    model = "I2V-01"

    with open(img_path, "rb") as image_file:
        data = base64.b64encode(image_file.read()).decode('utf-8')

    payload = json.dumps({
      "prompt": prompt,
      "model": model,
      "first_frame_image":f"data:image/jpeg;base64,{data}"
    })
    headers = {
      'authorization': 'Bearer ' + api_key,
      'content-type': 'application/json',
    }
    response = requests.request("POST", url, headers=headers, data=payload)
    print(response.text)
    task_id = response.json()['task_id']
    print("Video generation task submitted successfully, task ID.："+task_id)
    return task_id

def query_video_generation(task_id: str):
    url = "https://api.minimaxi.chat/v1/query/video_generation?task_id="+task_id
    headers = {
      'authorization': 'Bearer ' + api_key
    }
    response = requests.request("GET", url, headers=headers)
    status = response.json()['status']
    if status == 'Preparing':
        print("...Preparing...")
        return "", 'Preparing'   
    elif status == 'Queueing':
        print("...In the queue...")
        return "", 'Queueing'
    elif status == 'Processing':
        print("...Generating...")
        return "", 'Processing'
    elif status == 'Success':
        return response.json()['file_id'], "Finished"
    elif status == 'Fail':
        return "", "Fail"
    else:
        return "", "Unknown Status"


def fetch_video_result(file_id: str, output_file_name):
    print("---------------Video generated successfully, downloading now---------------")
    url = "https://api.minimaxi.chat/v1/files/retrieve?file_id="+file_id
    headers = {
        'authorization': 'Bearer '+api_key,
    }

    response = requests.request("GET", url, headers=headers)
    print(response.text)

    download_url = response.json()['file']['download_url']
    print("Video download link：" + download_url)
    with open(output_file_name, 'wb') as f:
        f.write(requests.get(download_url).content)
    print("THe video has been downloaded in："+os.getcwd()+'/'+output_file_name)


def generate_videos(data, folder):
    scenes = data['prompts']
    for idx, scene in enumerate(scenes, start=1):
        prompt = scene['prompt']
        output_file_name = f"{folder}/{idx}.mp4"
        if scene['img_src'] != "":
            #print(scene['img_src'])
            ##print(prompt)
            #print("#####")
            #print(scene['img_src'])
            #hanoishano
            """
            image_path = scene['img_src']  # Path to your input image
            output_path = output_file_name  # Path to save the generated video
            #zoom_point = (342, 342)  # x, y point to zoom out from
            img = cv2.imread(image_path)
            zoom_point = (img.shape[1] // 2, img.shape[0] // 2)
            duration = 6  # Video duration in seconds
            fps = 60  # Frames per second
            initial_zoom_factor = 2.00  # Initial zoom level

            generate_zoom_in_video(image_path, output_path, zoom_point, duration, fps, initial_zoom_factor)
            #generate_zoom_in_video(image_path, output_path, zoom_point, duration, fps, initial_zoom_factor)
            continue
            """
            
            prompt = "Give motion to the scene"
            task_id = invoke_imgtovid_generation(prompt, scene['img_src'])
            print("-----------------Image to Video generation task submitted -----------------")
            while True:
                time.sleep(10)
                file_id, status = query_video_generation(task_id)
                if file_id != "":
                    fetch_video_result(file_id, output_file_name)
                    print("---------------Successful---------------")
                    break
                elif status == "Fail" or status == "Unknown":
                    print("---------------Failed---------------")
                    break
        else:
            task_id = invoke_video_generation(prompt)
            print("-----------------Video generation task submitted -----------------")
            while True:
                time.sleep(10)
                file_id, status = query_video_generation(task_id)
                if file_id != "":
                    fetch_video_result(file_id, output_file_name)
                    print("---------------Successful---------------")
                    break
                elif status == "Fail" or status == "Unknown":
                    print("---------------Failed---------------")
                    break


