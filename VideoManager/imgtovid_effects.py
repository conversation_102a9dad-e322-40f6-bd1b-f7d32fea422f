import cv2
import numpy as np

def generate_zoom_in_video(
    image_path, output_path, zoom_point, duration, fps=30, zoom_factor=2.0
):
    """
    Generates a video that zooms into a given point on the image.

    :param image_path: Path to the input image.
    :param output_path: Path to save the generated video.
    :param zoom_point: Tuple (x, y) specifying the point to zoom into.
    :param duration: Duration of the video in seconds.
    :param fps: Frames per second of the output video.
    :param zoom_factor: Total zoom level (e.g., 2.0 for doubling the zoom).
    """
    # Load the image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError("Image not found at the specified path.")

    height, width, _ = image.shape
    x_center, y_center = zoom_point

    # Ensure the zoom point is within bounds
    x_center = min(max(x_center, 0), width - 1)
    y_center = min(max(y_center, 0), height - 1)

    # Prepare video writer
    fourcc = cv2.VideoWriter_fourcc(*"mp4v")
    video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    total_frames = int(duration * fps)

    for i in range(total_frames):
        # Compute the zoom level for the current frame
        zoom_level = 1 + (zoom_factor - 1) * (i / total_frames)

        # Calculate the cropping box
        crop_width = int(width / zoom_level)
        crop_height = int(height / zoom_level)
        x1 = max(0, x_center - crop_width // 2)
        y1 = max(0, y_center - crop_height // 2)
        x2 = min(width, x1 + crop_width)
        y2 = min(height, y1 + crop_height)

        # Crop and resize the frame
        cropped_frame = image[y1:y2, x1:x2]
        resized_frame = cv2.resize(cropped_frame, (width, height), interpolation=cv2.INTER_LINEAR)

        # Write the frame to the video
        video_writer.write(resized_frame)

    video_writer.release()
    print(f"Video saved to {output_path}")


def generate_zoom_out_video(
    image_path, output_path, zoom_point, duration, fps=30, initial_zoom_factor=2.0
):
    """
    Generates a video that starts zoomed in on a point and zooms out to show the full image.

    :param image_path: Path to the input image.
    :param output_path: Path to save the generated video.
    :param zoom_point: Tuple (x, y) specifying the point to zoom out from.
    :param duration: Duration of the video in seconds.
    :param fps: Frames per second of the output video.
    :param initial_zoom_factor: Initial zoom level (e.g., 2.0 for doubling the zoom).
    """
    # Load the image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError("Image not found at the specified path.")

    height, width, _ = image.shape
    x_center, y_center = zoom_point

    # Ensure the zoom point is within bounds
    x_center = min(max(x_center, 0), width - 1)
    y_center = min(max(y_center, 0), height - 1)

    # Prepare video writer
    fourcc = cv2.VideoWriter_fourcc(*"mp4v")
    video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    total_frames = int(duration * fps)

    for i in range(total_frames):
        # Compute the zoom level for the current frame
        zoom_level = initial_zoom_factor - (initial_zoom_factor - 1) * (i / total_frames)

        # Calculate the cropping box
        crop_width = int(width / zoom_level)
        crop_height = int(height / zoom_level)
        x1 = max(0, x_center - crop_width // 2)
        y1 = max(0, y_center - crop_height // 2)
        x2 = min(width, x1 + crop_width)
        y2 = min(height, y1 + crop_height)

        # Crop and resize the frame
        cropped_frame = image[y1:y2, x1:x2]
        resized_frame = cv2.resize(cropped_frame, (width, height), interpolation=cv2.INTER_LINEAR)

        # Write the frame to the video
        video_writer.write(resized_frame)

    video_writer.release()
    print(f"Video saved to {output_path}")