import json
import os
import spacy

def get_image_extension(base_name, random_uuid):
    """Return extension if image exists, otherwise None"""
    for ext in ['png', 'jpeg', 'jpg']:
        if os.path.exists(f"./{random_uuid}/{base_name}.{ext}"):
            return ext
    return None

def add_company_imgs(data, random_uuid):
    # Load NLP model
    nlp = spacy.load("en_core_web_md")

    # Check for available images
    team_ext = get_image_extension("team_img", random_uuid)
    office_ext = get_image_extension("office_img", random_uuid)

    # Load target words once
    team_doc = nlp("team")
    office_doc = nlp("office")

    # Collect items with their similarity scores
    items_with_scores = []
    for item in data["prompts"]:
        combined_text = f"{item['text']} {item['prompt']}"
        doc = nlp(combined_text)
        
        team_sim = doc.similarity(team_doc)
        office_sim = doc.similarity(office_doc)
        
        items_with_scores.append({
            "text": item['text'],
            "item": item,
            "team_sim": team_sim,
            "office_sim": office_sim
        })

    # Assign team image to item with highest team similarity
    if team_ext and items_with_scores:
        max_team = max(items_with_scores, key=lambda x: x["team_sim"])
        items_with_scores.remove(max_team)
        max_team["item"]["img_src"] = f"team_img.{team_ext}"

    # Assign office image to item with highest office similarity
    if office_ext and items_with_scores:
        max_office = max(items_with_scores, key=lambda x: x["office_sim"])
        max_office["item"]["img_src"] = f"office_img.{office_ext}"

    new_lst = []
    for item in data['prompts']:
        if team_ext and item['text'] == max_team['text']:
            n_item = item
            n_item["img_src"] = max_team["item"]["img_src"]
            new_lst.append(n_item)
        elif office_ext and item['text'] == max_office['text']:
            n_item = item
            n_item["img_src"] = max_office['item']["img_src"]
            new_lst.append(n_item)
        else:
            n_item = item
            n_item["img_src"] = ""
            new_lst.append(n_item)

    new_json = {"prompts": new_lst}
    #print(new_json)
    #new_data = json.loads(new_json)
    # Save modified data
    
    print("Processing completed.")
    return new_json

    