FROM python:3.12.4

# Set working directory inside container
WORKDIR /app

# Install system dependencies and clean up
RUN apt-get update && apt-get install -y \
    imagemagick \
    ffmpeg \
    fonts-roboto \
    && rm -f /etc/ImageMagick-6/policy.xml || true \
    && rm -rf /var/lib/apt/lists/*
# Upgrade pip-related tools
RUN pip install --upgrade pip setuptools wheel

# Copy only requirements first to leverage cache
COPY requirements.txt .

# Install dependencies
RUN pip install -r requirements.txt

# Download NLP model for spaCy
RUN python -m spacy download en_core_web_md

# Copy everything
COPY . .

# Set the default command (Gunicorn web server)
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app", "--timeout", "3000"]
