from moviepy.editor import VideoFileClip, concatenate_videoclips
import glob
import os

def combine_videos(input_folder, output_file, method="compose"):
    # Get a sorted list of all MP4 files in the folder
    video_paths = sorted(glob.glob(os.path.join(input_folder, "*.mp4")))
    if not video_paths:
        print("No MP4 files found in", input_folder)
        return

    # Load each video as a VideoFileClip
    clips = [VideoFileClip(path) for path in video_paths]

    # If your clips have different sizes or audio issues, using the "compose" method
    # will reframe them to match the size of the first clip.
    final_clip = concatenate_videoclips(clips, method=method)

    # Write the final video to output_file
    final_clip.write_videofile(output_file, codec="libx264", audio_codec="aac")

    # Close the clips to free resources
    for clip in clips:
        clip.close()

if __name__ == "__main__":
    input_dir = "./combined"        # Folder containing your MP4 files
    output_path = "final_output.mp4"
    combine_videos(input_dir, output_path)
