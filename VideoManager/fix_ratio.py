from moviepy.editor import VideoFileClip
import subprocess

def get_rotation(file_path):
    """Get video rotation metadata using FFprobe."""
    try:
        cmd = f"ffprobe -v error -select_streams v:0 -show_entries side_data=rotation -of csv=p=0 {file_path}"
        rotation = subprocess.check_output(cmd, shell=True).decode().strip()
        return int(rotation) if rotation else 0
    except:
        return 0

def process_video(input_path, output_crop_path):
    # Load video with rotation handling
    with VideoFileClip(input_path) as clip:
        rotation = get_rotation(input_path)
        if rotation in [90, 270]:
            # Swap dimensions if video is rotated
            width, height = clip.size[1], clip.size[0]
            clip = clip.rotate(-rotation)  # Correct rotation
        else:
            width, height = clip.size

        # Option 2: Smart crop to 1:1 ratio
        target_size = min(max(width, height), 720) if min(width, height) >= 720 else min(width, height)
        
        # Calculate crop coordinates
        if width > height:
            # Landscape: crop width
            x1, x2 = (width - target_size) // 2, (width + target_size) // 2
            cropped_clip = clip.crop(x1=x1, x2=x2, y1=0, y2=height)
        else:
            # Portrait: crop height
            y1, y2 = (height - target_size) // 2, (height + target_size) // 2
            cropped_clip = clip.crop(y1=y1, y2=y2, x1=0, x2=width)

        # Resize if needed (only if cropped size > 720)
        if target_size > 720:
            cropped_clip = cropped_clip.resize(newsize=(720, 720))
            
        cropped_clip.write_videofile(output_crop_path)

"""
# Usage example
process_video(
    input_path="vid_path.mp4",
    output_crop_path="vid_path_cropped.mp4"
)"
"""