from moviepy.editor import VideoFileClip, AudioFileClip, CompositeAudioClip
import os
import shutil

test = os.getenv("TEST")
print(f"HERE IS TEST: {test}")

def add_music(path_to_vid, path_to_music, output_path):
    # Ensure the output directory exists
    output_dir = os.path.dirname(output_path)
    os.makedirs(output_dir, exist_ok=True)

    video = VideoFileClip(path_to_vid)

    # Load the background music and trim it to the video's duration
    background_audio = AudioFileClip(path_to_music)
    background_audio = background_audio.subclip(0, min(video.duration,background_audio.duration))

    background_audio = background_audio.volumex(0.1)

    # Mix the original audio with the background music
    new_audio = CompositeAudioClip([video.audio, background_audio])

    final_video = video.set_audio(new_audio)

    final_video.write_videofile(output_path, codec="libx264", audio_codec="aac")

    # Clean up: remove the output directory and all its contents

    dir_to_delete = os.path.dirname(path_to_vid)
    print(f"DIR TO DELETE {dir_to_delete}")
    shutil.rmtree(dir_to_delete, ignore_errors=True)


